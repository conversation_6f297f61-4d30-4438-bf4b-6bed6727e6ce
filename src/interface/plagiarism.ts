// 查重对比相关接口定义

// 句子匹配结果
interface SentenceMatch {
    id: string;
    sourceBookId: string;
    sourceBookName: string;
    sourcePage: number;
    sourceCardId?: number; // 源卡片ID
    sourceContent: string;
    targetBookId: string;
    targetBookName: string;
    targetPage: number;
    targetCardId?: number; // 目标卡片ID
    targetContent: string;
    similarity: number; // 相似度分数 0-1
    matchType: 'exact' | 'similar' | 'partial'; // 匹配类型
    createTime: string;
}

// 查重批次
interface PlagiarismBatch {
    id: string;
    name: string;
    description?: string;
    bookIds: string[]; // 参与对比的书籍ID列表
    totalMatches: number;
    status: 'pending' | 'processing' | 'completed' | 'failed';
    createTime: string;
    updateTime: string;
    completedTime?: string;
    progress?: number; // 进度百分比 0-100
}

// 查重对比参数
interface PlagiarismCompareParams {
    bookIds: string[]; // 要对比的书籍ID列表
    batchName: string;
    description?: string;
    similarityThreshold: number; // 相似度阈值 0-1
    minSentenceLength: number; // 最小句子长度
    enableExactMatch: boolean; // 是否启用精确匹配
    enableSimilarMatch: boolean; // 是否启用相似匹配
    enablePartialMatch: boolean; // 是否启用部分匹配
}

// 查重结果查询参数
interface PlagiarismResultParams {
    batchId: string;
    pageNo: number;
    pageSize: number;
    minSimilarity?: number;
    maxSimilarity?: number;
    matchType?: 'exact' | 'similar' | 'partial';
    sourceBookId?: string;
    targetBookId?: string;
}

// 分页查重结果
interface PlagiarismResult {
    totals: number;
    pageNo: number;
    pageCount: number;
    pageSize: number;
    list: SentenceMatch[];
    batchInfo: PlagiarismBatch;
}

// 批次列表查询参数
interface BatchListParams {
    pageNo: number;
    pageSize: number;
    status?: 'pending' | 'processing' | 'completed' | 'failed';
    keyword?: string; // 搜索批次名称或描述
}

// 分页批次结果
interface BatchListResult {
    totals: number;
    pageNo: number;
    pageCount: number;
    pageSize: number;
    list: PlagiarismBatch[];
}

// 批次统计信息
interface BatchStatistics {
    batchId: string;
    totalBooks: number;
    totalSentences: number;
    totalMatches: number;
    exactMatches: number;
    similarMatches: number;
    partialMatches: number;
    averageSimilarity: number;
    highSimilarityMatches: number; // 高相似度匹配数量 (>0.8)
    mediumSimilarityMatches: number; // 中等相似度匹配数量 (0.5-0.8)
    lowSimilarityMatches: number; // 低相似度匹配数量 (<0.5)
}

export type {
    SentenceMatch,
    PlagiarismBatch,
    PlagiarismCompareParams,
    PlagiarismResultParams,
    PlagiarismResult,
    BatchListParams,
    BatchListResult,
    BatchStatistics
};
