use actix_web::{web, HttpRequest, HttpResponse, Result};
use actix_web::http::header;
use futures_util::StreamExt;
use std::time::Duration;
use tokio::time::interval;
use tokio::sync::broadcast;
use serde_json::Value;

/// SSE事件流处理
pub async fn plagiarism_events_sse(req: HttpRequest) -> Result<HttpResponse> {
    let (tx, mut rx) = broadcast::channel::<String>(100);
    
    // 获取全局WebSocket广播通道的接收器
    let ws_rx = {
        use crate::controller::websocket::BROADCAST_CHANNEL;
        let channel = BROADCAST_CHANNEL.read().await;
        channel.subscribe()
    };

    // 启动一个任务来转发WebSocket消息到SSE
    let tx_clone = tx.clone();
    tokio::spawn(async move {
        let mut ws_receiver = ws_rx;
        while let Ok(msg) = ws_receiver.recv().await {
            if let Ok(msg_str) = std::str::from_utf8(&msg) {
                if let Ok(parsed) = serde_json::from_str::<Value>(msg_str) {
                    if let Some(event) = parsed.get("event").and_then(|e| e.as_str()) {
                        if event.starts_with("batch-") {
                            let _ = tx_clone.send(msg_str.to_string());
                        }
                    }
                }
            }
        }
    });

    // 创建SSE响应流
    let stream = async_stream::stream! {
        // 发送初始连接消息
        yield Ok::<_, actix_web::Error>(
            web::Bytes::from(format!("data: {}\n\n", serde_json::json!({
                "type": "connected",
                "message": "Connected to plagiarism events"
            })))
        );

        let mut heartbeat = interval(Duration::from_secs(30));
        
        loop {
            tokio::select! {
                // 接收广播消息
                msg = rx.recv() => {
                    match msg {
                        Ok(data) => {
                            if let Ok(parsed) = serde_json::from_str::<Value>(&data) {
                                if let Some(event) = parsed.get("event").and_then(|e| e.as_str()) {
                                    if let Some(payload) = parsed.get("payload") {
                                        yield Ok(web::Bytes::from(format!(
                                            "event: {}\ndata: {}\n\n",
                                            event,
                                            payload
                                        )));
                                    }
                                }
                            }
                        }
                        Err(_) => break,
                    }
                }
                
                // 心跳
                _ = heartbeat.tick() => {
                    yield Ok(web::Bytes::from("data: {\"type\":\"heartbeat\"}\n\n"));
                }
            }
        }
    };

    Ok(HttpResponse::Ok()
        .insert_header((header::CONTENT_TYPE, "text/event-stream"))
        .insert_header((header::CACHE_CONTROL, "no-cache"))
        .insert_header((header::CONNECTION, "keep-alive"))
        .insert_header(("Access-Control-Allow-Origin", "*"))
        .insert_header(("Access-Control-Allow-Headers", "Cache-Control"))
        .streaming(stream))
}

/// 处理plagiarism相关的API路由
pub async fn ctrl_plagiarism_compare(req_body: web::Json<Value>) -> Result<HttpResponse> {
    // 这里可以添加plagiarism compare的HTTP API处理逻辑
    // 目前主要通过Tauri命令处理，这里作为备用
    Ok(HttpResponse::Ok().json(serde_json::json!({
        "message": "Plagiarism compare endpoint - use Tauri commands for main functionality"
    })))
}

pub async fn ctrl_plagiarism_results(req_body: web::Json<Value>) -> Result<HttpResponse> {
    // 这里可以添加plagiarism results的HTTP API处理逻辑
    Ok(HttpResponse::Ok().json(serde_json::json!({
        "message": "Plagiarism results endpoint - use Tauri commands for main functionality"
    })))
}

pub async fn ctrl_plagiarism_batches(req_body: web::Json<Value>) -> Result<HttpResponse> {
    // 这里可以添加plagiarism batches的HTTP API处理逻辑
    Ok(HttpResponse::Ok().json(serde_json::json!({
        "message": "Plagiarism batches endpoint - use Tauri commands for main functionality"
    })))
}

pub async fn ctrl_plagiarism_batch_detail(path: web::Path<String>) -> Result<HttpResponse> {
    let _batch_id = path.into_inner();
    // 这里可以添加获取批次详情的HTTP API处理逻辑
    Ok(HttpResponse::Ok().json(serde_json::json!({
        "message": "Plagiarism batch detail endpoint - use Tauri commands for main functionality"
    })))
}

pub async fn ctrl_plagiarism_batch_statistics(path: web::Path<String>) -> Result<HttpResponse> {
    let _batch_id = path.into_inner();
    // 这里可以添加获取批次统计的HTTP API处理逻辑
    Ok(HttpResponse::Ok().json(serde_json::json!({
        "message": "Plagiarism batch statistics endpoint - use Tauri commands for main functionality"
    })))
}

pub async fn ctrl_plagiarism_delete_batch(path: web::Path<String>) -> Result<HttpResponse> {
    let _batch_id = path.into_inner();
    // 这里可以添加删除批次的HTTP API处理逻辑
    Ok(HttpResponse::Ok().json(serde_json::json!({
        "message": "Plagiarism delete batch endpoint - use Tauri commands for main functionality"
    })))
}

pub async fn ctrl_plagiarism_cancel_comparison(path: web::Path<String>) -> Result<HttpResponse> {
    let _batch_id = path.into_inner();
    // 这里可以添加取消查重任务的HTTP API处理逻辑
    Ok(HttpResponse::Ok().json(serde_json::json!({
        "message": "Plagiarism cancel comparison endpoint - use Tauri commands for main functionality"
    })))
}
